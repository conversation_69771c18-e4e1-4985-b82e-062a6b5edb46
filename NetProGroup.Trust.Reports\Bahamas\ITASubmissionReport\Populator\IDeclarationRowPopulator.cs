// <copyright file="IDeclarationRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Interface for the IRD submission report declaration row populator.
    /// </summary>
    public interface IDeclarationRowPopulator : ITransientService
    {
        void Populate(IXLWorksheet worksheet, List<Submission> submissions);
    }
}