﻿// <copyright file="SubmissionReportsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.FormDocuments;
using NetProGroup.Trust.Shared.Jurisdictions;
using System.Linq.Expressions;

namespace NetProGroup.Trust.DataManager.Submissions
{
    /// <summary>
    /// Manager for getting submission data for reporting.
    /// </summary>
    public class SubmissionReportsDataManager : ISubmissionReportsDataManager
    {
        private readonly ILogger _logger;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly IFormTemplatesRepository _formTemplatesRepository;
        private readonly ISubmissionsRepository _submissionsRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionReportsDataManager"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="legalEntitiesRepository">Repository for legal entities.</param>
        /// <param name="formTemplatesRepository">Repository for form templates.</param>
        /// <param name="jurisdictionsRepository">Repository for jurisdictions.</param>
        /// <param name="submissionsRepository">Repository for submissions.</param>
        public SubmissionReportsDataManager(ILogger<SubmissionReportsDataManager> logger,
                                            ILegalEntitiesRepository legalEntitiesRepository,
                                            IFormTemplatesRepository formTemplatesRepository,
                                            IJurisdictionsRepository jurisdictionsRepository,
                                            ISubmissionsRepository submissionsRepository)
        {
            _logger = logger;
            _legalEntitiesRepository = legalEntitiesRepository;
            _formTemplatesRepository = formTemplatesRepository;
            _jurisdictionsRepository = jurisdictionsRepository;
            _submissionsRepository = submissionsRepository;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Submission>> GetSubmissionForNevisIRDReportExportAsync(IEnumerable<Guid> submissionIds, int financialYear)
        {
            return await _submissionsRepository.GetQueryable()
                .Include(s => s.LegalEntity)
                    .ThenInclude(le => le.Jurisdiction)
                .Include(s => s.FormDocument)
                    .ThenInclude(fd => fd.FormDocumentRevisions)
                .Include(s => s.Attributes)
                .Include(s => s.Invoice)
                    .ThenInclude(i => i.PaymentInvoices)
                        .ThenInclude(pi => pi.Payment)
                .Where(s => s.FinancialYear == financialYear && submissionIds.Contains(s.Id) &&
                            (s.Invoice.PaymentInvoices.All(pi => pi.Payment.PaidAt.HasValue) || s.IsPaid) &&
                            s.FormDocument.FormDocumentRevisions.All(fr => fr.Status == FormDocumentRevisionStatus.Finalized))
                .AsSplitQuery()
                .ToListAsync();
        }


        /// <inheritdoc/>
        public async Task<IEnumerable<Submission>> GetSubmissionForBahamasIRDReportExportAsync(IEnumerable<Guid> submissionIds)
        {
            // Create the submission predicate
            Expression<Func<Submission, bool>> predicate = s => submissionIds.Contains(s.Id);

            return await _submissionsRepository.FindByConditionAsync(predicate,
                options: o => o.Include(s => s.LegalEntity.Jurisdiction)
                               .Include(s => s.LegalEntity.MasterClient)
                               .Include(s => s.FormDocument.FormDocumentRevisions)
                               .Include(s => s.Attributes)
                               .Include(s => s.Invoice.PaymentInvoices)
                               .ThenInclude(pi => pi.Payment)
                               .TagWithCallSite()
                               .AsSplitQuery());
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Submission>> GetSubmissionsForNevisFinancialReportExport()
        {
            // Retrieve the Nevis jurisdiction
            var jurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Nevis);

            var query = await _submissionsRepository.FindByConditionAsync(s =>

                // Filter submissions by jurisdiction
                s.LegalEntity.JurisdictionId == jurisdiction.Id &&

                // Primary filter: Only process non-export invoices,
                // it should include yesterday , if it fails
                !s.ExportedInFinancialReportId.HasValue,
                q => q
                    .Include(s => s.LegalEntity).ThenInclude(le => le.Jurisdiction)
                    .Include(s => s.Invoice)
                    .Include(s => s.Invoice.InvoiceLines)

                    // Apply materialization
                    .AsSplitQuery() // Optimize large object graphs
                    .TagWithCallSite()); // Add query tag for monitoring

            return query.ToList();
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Submission>> GetSubmissionsForPanamaBasicFinancialReportExport()
        {
            // Retrieve the Panama jurisdiction
            var jurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Panama);

            var query = await _submissionsRepository.FindByConditionAsync(s =>

            // Filter submissions by jurisdiction
            s.LegalEntity.JurisdictionId == jurisdiction.Id &&
            s.FormDocument.Attributes.Any(a =>
                    a.Key == WellKnownFormDocumentAttibuteKeys.UseAccountingTool &&
                    a.Value == "true") &&

                    // Primary filter: Only process non-export invoices,
                    // it should include yesterday , if it fails
                    !s.ExportedInFinancialReportId.HasValue,
                q => q
                    .Include(s => s.LegalEntity).ThenInclude(le => le.Jurisdiction)
                    .Include(s => s.Invoice)
                    .Include(s => s.Invoice.InvoiceLines)

                    // Apply materialization
                    .AsSplitQuery() // Optimize large object graphs
                    .TagWithCallSite()); // Add query tag for monitoring

            return query.ToList();
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Submission>> GetSubmissionsForBahamasExport()
        {
            // Retrieve the Bahamas jurisdiction
            var jurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Bahamas);

            var query = await _submissionsRepository.FindByConditionAsync(s =>

            // Filter submissions by jurisdiction
            s.LegalEntity.JurisdictionId == jurisdiction.Id &&
            s.FormDocument.Attributes.Any(a =>
                    a.Key == WellKnownFormDocumentAttibuteKeys.UseAccountingTool &&
                    a.Value == "true") &&

                    // Primary filter: Only process non-export invoices,
                    // it should include yesterday , if it fails
                    !s.ExportedInFinancialReportId.HasValue,
                q => q
                    .Include(s => s.LegalEntity)
                    .Include(s => s.Invoice)
                    .Include(s => s.Invoice.InvoiceLines)

                    // Apply materialization
                    .AsSplitQuery() // Optimize large object graphs
                    .TagWithCallSite()); // Add query tag for monitoring

            return query.ToList();
        }

        /// <inheritdoc />
        public async Task CompleteFinancialExport(IEnumerable<Submission> submissions, Guid reportId)
        {
            ArgumentNullException.ThrowIfNull(submissions, nameof(submissions));

            foreach (var submission in submissions)
            {
                submission.ExportedInFinancialReportId = reportId;
            }

            await _submissionsRepository.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public async Task<AllSubmissionYearsDTO> GetAllSubmissionYears(AllSubmissionYearsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));

            var result = new AllSubmissionYearsDTO
            {
                ModuleId = request.ModuleId
            };

            var years = new List<int>();
            var currentYear = DateTime.Today.Year;

            // Get the template for the module
            var template = await _formTemplatesRepository.FindFirstOrDefaultByConditionAsync(ft => ft.ModuleId == request.ModuleId,
                                                                                             q => q.Include(ft => ft.FormTemplateVersions));

            foreach (var year in template.FormTemplateVersions.Where(ftv => ftv.Year.HasValue).OrderBy(ftv => ftv.Year).Select(ftv => ftv.Year.Value))
            {
                if (year < currentYear)
                {
                    if (!years.Contains(year))
                    {
                        years.Add(year);
                    }
                }
            }

            result.Years = years;
            return result;
        }
    }
}