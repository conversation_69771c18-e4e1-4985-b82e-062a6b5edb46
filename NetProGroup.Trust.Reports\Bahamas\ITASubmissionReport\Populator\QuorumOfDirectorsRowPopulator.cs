// <copyright file="QuorumOfDirectorsRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class QuorumOfDirectorsRowPopulator : LinePopulatorBase, IQuorumOfDirectorsRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var form = data.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;
            var relevantActivityIndexes = GetRelevantActivityIndexes(form!.DataSet);

            List<int> GetRelevantActivityIndexes(Dictionary<string, string> dataSet)
            {
                var pattern = $@"{FormKeys.RelevantActivitiesRegexPrefix}\.(\d+)\.";
                return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
            }

            foreach (var index in relevantActivityIndexes)
            {
                // Check if the activity was selected
                var isSelected = bool.Parse(GetValueOrDefault(form, FormKeys.RelevantActivitiesIsSelected(index), "false"));

                if (isSelected)
                {
                    // Retrieve the name
                    var relevantActivity = GetValueOrDefault(form, FormKeys.RelevantActivitiesLabel(index));

                    // Check the selected activity
                    string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                    // Get director indexes for this relevant activity
                    var directorIndexes = GetDirectorIndexes(form!.DataSet, relevantActivityKey);

                    List<int> GetDirectorIndexes(Dictionary<string, string> dataSet, string activityKey)
                    {
                        var pattern = $@"{activityKey.Replace(".", "\\.", StringComparison.Ordinal)}{FormKeys.DirectorsRegexSuffix}\.(\d+)\.";
                        return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
                    }

                    foreach (var directorIndex in directorIndexes)
                    {
                        // Retrieve the entity unique id
                        SetCellValueAndStyle(worksheet, currentRow, 1, GetValueOrDefault(form, FormKeys.EntityDetailsEntityId));

                        // Retrieve the name
                        SetCellValueAndStyle(worksheet, currentRow, 2, relevantActivity);

                        SetCellValueAndStyle(worksheet, currentRow, 3, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsEndDate));

                        // Retrieve the meeting numbers - this needs special handling as it's a collection within a director
                        // For now, we'll leave this as a placeholder since the pattern for nested collections within directors isn't clear
                        SetCellValueAndStyle(worksheet, currentRow, 4, ""); // TODO: Handle meeting numbers collection

                        // Retrieve the director name
                        var directorName = GetValueOrDefault(form, FormKeys.DirectorsName(relevantActivityKey, directorIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 5, directorName);

                        // Retrieve if the director was physically present in the meeting
                        var physicallyPresentInBahamas = GetValueOrDefault(form, FormKeys.DirectorsPhysicallyPresentInBahamas(relevantActivityKey, directorIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 6, physicallyPresentInBahamas);

                        // Retrieve the director qualification
                        var qualification = GetValueOrDefault(form, FormKeys.DirectorsQualification(relevantActivityKey, directorIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 7, qualification);

                        // Retrieve the years of experience
                        var yearsOfExperience = GetValueOrDefault(form, FormKeys.DirectorsYearsOfExperience(relevantActivityKey, directorIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 8, yearsOfExperience);

                        currentRow += 1;
                    }
                }
            }
        }
    }
}