﻿// <copyright file="SubmissionReportsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Modules;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Permissions;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Factory;
using NetProGroup.Trust.Reports.Nevis.SimplifiedTaxReturn;
using NetProGroup.Trust.Reports.Panama.BasicFinancialReport;
using NetProGroup.Trust.Reports.Panama.SubmissionDataReport;
using NetProGroup.Trust.Reports.Bahamas.EconomicSubstance;
using NetProGroup.Trust.Reports.Bahamas.EconomicSubstanceReport;

namespace NetProGroup.Trust.Application.AppServices.Submissions
{
    /// <summary>
    /// Application service for submission reporting.
    /// </summary>
    public class SubmissionReportsAppService : ISubmissionReportsAppService
    {
        private readonly IWorkContext _workContext;
        private readonly IBasicFinancialReportGenerator _basicFinancialReportGenerator;
        private readonly ISimplifiedTaxReturnGenerator _simplifiedTaxReturnGenerator;
        private readonly IEconomicSubstanceReportGenerator _economicSubstanceReportGenerator;
        private readonly ISubmissionDataReportGenerator _submissionDataReportGenerator;
        private readonly ISubmissionsManager _submissionsManager;
        private readonly IModulesDataManager _modulesDataManager;
        private readonly IExportSubmissionsFactory _exportFactory;
        private readonly ISecurityManager _securityManager;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionReportsAppService"/> class.
        /// </summary>
        /// <param name="mapper">A mapper instance.</param>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="basicFinancialReportGenerator">The report generator for the basic financial report module.</param>
        /// <param name="simplifiedTaxReturnGenerator">The report generator for the simplified tax return module.</param>
        /// <param name="submissionDataReportGenerator">The report generator for the submission data.</param>
        /// <param name="exportFactory">The export factory.</param>
        /// <param name="economicSubstanceReportGenerator">The report generator for the economic substance report.</param>
        /// <param name="submissionsManager">The manager to use for submissions.</param>
        /// <param name="modulesDataManager">The manager to use for modules.</param>
        /// <param name="securityManager">The security manager.</param>
        public SubmissionReportsAppService(
            IMapper mapper,
            IWorkContext workContext,
            IBasicFinancialReportGenerator basicFinancialReportGenerator,
            ISimplifiedTaxReturnGenerator simplifiedTaxReturnGenerator,
            ISubmissionDataReportGenerator submissionDataReportGenerator,
            IEconomicSubstanceReportGenerator economicSubstanceReportGenerator,
            ISubmissionsManager submissionsManager,
            IModulesDataManager modulesDataManager,
            IExportSubmissionsFactory exportFactory,
            ISecurityManager securityManager)
        {
            _mapper = mapper;
            _workContext = workContext;

            _basicFinancialReportGenerator = basicFinancialReportGenerator;
            _simplifiedTaxReturnGenerator = simplifiedTaxReturnGenerator;
            _submissionDataReportGenerator = submissionDataReportGenerator;
            _economicSubstanceReportGenerator = economicSubstanceReportGenerator;
            _submissionsManager = submissionsManager;
            _modulesDataManager = modulesDataManager;
            _exportFactory = exportFactory;
            _securityManager = securityManager;
        }

        /// <inheritdoc/>
        public async Task<ReportDownloadResponseDTO> ExportSubmissionForIRDAsync(ExportSubmissionDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotNullOrEmpty(request.SubmissionIds, nameof(request.SubmissionIds));

            var identityUserId = _workContext.IdentityUserId.Value;

            // Authorization
            var moduleKey = request.Module;
            var module = await _modulesDataManager.GetModuleByKeyAsync(moduleKey);
            var requiredPermission = moduleKey switch
            {
                ModuleKeyConsts.SimplifiedTaxReturn => WellKnownPermissionNames.STRModule_Submissions_Export_IRD,
                ModuleKeyConsts.EconomicSubstanceBahamas => WellKnownPermissionNames.ESBahamasModule_Submissions_Export_ITA,
                _ => throw new PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(), $"Export of submissions not supported for module '{moduleKey}'")
            };

            await _securityManager.RequireManagementPermissionAsync(requiredPermission);

            // Export
            var exporter = _exportFactory.CreateExportGenerator(request);
            var exportSubmissionResponseDto = await exporter.ExportAsync(request);

            await _submissionsManager.UpdateSubmissionExport(request.SubmissionIds, identityUserId, module.Id);

            return exportSubmissionResponseDto;
        }

        /// <inheritdoc/>
        [Obsolete("Use ExportSubmissionForIRDAsync instead")]
        public async Task<ReportDownloadResponseDTO> ExportSubmissionsForPanamaAsync(SubmissionDataRequestDTO request)
        {
            // TODO remove this method and use ExportSubmissionForIRDAsync instead

            ArgumentNullException.ThrowIfNull(request, nameof(request));

            // Update the report status for the retrieved submissions
            var identityUserId = _workContext.IdentityUserId.Value;

            var requiredPermission = WellKnownPermissionNames.BFRPanamaModule_Submissions_Export;
            await _securityManager.RequireManagementPermissionAsync(requiredPermission);

            // Retrieve the authorized jurisdictions
            var authorizedJurisdictionIds = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            var submissionRequest = new RetrieveSubmissionsRequest()
            {
                SubmissionIds = request.SubmissionIds.ToList(),
                AuthorizedJurisdictionIDs = authorizedJurisdictionIds,
                IncludeDeleted = false // Reports should not include deleted submissions
            };

            var submissions = await _submissionsManager.RetrieveSubmissionsByIdsAsync(submissionRequest);

            var reportOutput = await _submissionDataReportGenerator.GenerateSubmissionDataReportAsync(submissions);

            var panamaModule = await _modulesDataManager.GetModuleByKeyAsync(ModuleKeyConsts.BasicFinancialReportPanama);

            await _submissionsManager.UpdateSubmissionExport(submissions.Select(s => s.Id), identityUserId, panamaModule.Id);

            var fileName = $"submission-data-report-{DateTime.UtcNow.ToString("MM-dd-yyyy")}.xlsx";

            var report = ReportDownloadResponseDTO.Create(fileName, reportOutput.FileContent);

            return report;
        }

        /// <inheritdoc/>
        public async Task<ReportDownloadResponseDTO> GenerateSubmissionsReportForNevisAsync(SubmissionsReportRequestNevisDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var requiredPermission = WellKnownPermissionNames.STRModule_Submissions_Export;
            await _securityManager.RequireManagementPermissionAsync(requiredPermission);

            // Create a SearchSubmissionsRequest entity.
            var searchRequest = _mapper.Map<SearchSubmissionsRequest>(request);

            // Retrieve the authorized jurisdictions
            searchRequest.AuthorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            // Reports should not include deleted submissions
            searchRequest.IsDeleted = false;

            var submissions = await _submissionsManager.FilterNevisSubmissionsForReportAsync(searchRequest);

            var reportOutput = await _simplifiedTaxReturnGenerator.GenerateSubmissionsReportAsync(submissions);

            var fileName = $"simplified-tax-return-submissions-{DateTime.UtcNow.ToString("MM-dd-yyyy")}.xlsx";

            var report = ReportDownloadResponseDTO.Create(fileName, reportOutput.FileContent);

            return report;
        }

        /// <inheritdoc/>
        public async Task<ReportDownloadResponseDTO> GenerateSubmissionsReportForBahamasAsync(SubmissionsReportRequestBahamasDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var requiredPermission = WellKnownPermissionNames.ESBahamasModule_Submissions_Export;

            // Create a SearchSubmissionsRequest entity filters.
            var filterSubmissionsRequest = _mapper.Map<FilterSubmissionsRequestForBahamas>(request);

            // Retrieve the authorized jurisdictions
            filterSubmissionsRequest.AuthorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            // Reports should not include deleted submissions
            filterSubmissionsRequest.IsDeleted = false;

            var submissions = await _submissionsManager.SearchSubmissionsForBahamasReportAsync(filterSubmissionsRequest);

            var reportOutput = await _economicSubstanceReportGenerator.GenerateSubmissionsReportAsync(submissions);

            var fileName = $"economic-substance-report-submissions-{DateTime.UtcNow.ToString("MM-dd-yyyy")}.xlsx";

            var report = ReportDownloadResponseDTO.Create(fileName, reportOutput.FileContent);

            return report;
        }

        /// <inheritdoc/>
        public async Task<ReportDownloadResponseDTO> GenerateSubmissionsReportForPanamaAsync(SubmissionsReportRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var requiredPermission = WellKnownPermissionNames.BFRPanamaModule_Submissions_Export;
            var authorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            var filterRequest = new FilterSubmissionsRequest()
            {
                GeneralSearchTerm = request.GeneralSearchTerm,
                SubmittedAfterDate = request.SubmittedAfterDate,
                SubmittedBeforeDate = request.SubmittedBeforeDate,
                FinancialPeriodStartAt = request.FinancialPeriodStartAt,
                FinancialPeriodEndAt = request.FinancialPeriodEndAt,
                IsPaid = request.IsPaid,
                IsUsingAccountingRecordsTool = request.IsUsingAccountingRecordsTool,
                AuthorizedJurisdictionIDs = authorizedJurisdictionIDs,
                IsDeleted = false // Reports should not include deleted submissions
            };

            var submissions = await _submissionsManager.FilterPanamaSubmissionsForReportAsync(filterRequest);

            var reportOutput = await _basicFinancialReportGenerator.GenerateSubmissionsReportAsync(submissions);

            var fileName = $"basic-financial-report-submissions-{DateTime.UtcNow.ToString("MM-dd-yyyy")}.xlsx";

            var report = ReportDownloadResponseDTO.Create(fileName, reportOutput.FileContent);

            return report;
        }
    }
}
