// <copyright file="IITASubmissionReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Annual;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport
{
    /// <summary>
    /// Interface for the IRD submission report generator.
    /// </summary>
    public interface IITASubmissionReportGenerator : IExportYearSubmissionsIRDGenerator, IScopedService;
}