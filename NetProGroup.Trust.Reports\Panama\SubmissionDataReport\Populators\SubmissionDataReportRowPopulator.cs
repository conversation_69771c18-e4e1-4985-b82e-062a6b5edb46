// <copyright file="SubmissionDataReportRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Helpers;
using NetProGroup.Trust.Domain.Shared.Reports;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Panama.SubmissionDataReport.Populators
{
    /// <summary>
    /// Populate a row for the submission data report.
    /// </summary>
    public class SubmissionDataReportRowPopulator : LinePopulatorBase, ISubmissionDataReportRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Set the submission id
            SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

            // Master client code
            SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

            // Company Entity number
            SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

            // Company Name
            SetCellValueAndStyle(worksheet, currentRow, 4, data.LegalEntity.Name);

            // Company incorporation number
            SetCellValueAndStyle(worksheet, currentRow, 5, data.LegalEntity.IncorporationNr);

            // Company referral office
            SetCellValueAndStyle(worksheet, currentRow, 6, data.LegalEntity.ReferralOffice);

            // Set the submission status
            SetCellValueAndStyle(worksheet, currentRow, 7, data.IsPaid ? "PAID" : data.Status.ToString());

            // Set the created by user email
            SetCellValueAndStyle(worksheet, currentRow, 8, data.Attributes.GetAttributeValue<string>(SubmissionAttributeKeys.CreatedByEmail));

            // Set the submitted by by user email
            SetCellValueAndStyle(worksheet, currentRow, 9, data.Attributes.GetAttributeValue<string>(SubmissionAttributeKeys.SubmittedByEmail));

            // Set the creation date
            SetCellValueAndStyle(worksheet, currentRow, 8, FormatDateAsLocalTime(data.CreatedAt, data));

            // Set the update date
            SetCellValueAndStyle(worksheet, currentRow, 9, FormatDateAsLocalTime(data.UpdatedAt, data));

            // Set the submitted at date.
            SetCellValueAndStyle(worksheet, currentRow, 10, FormatDateAsLocalTime(data.SubmittedAt, data));

            var paymentInformation = data.GetPaidPayment();

            if (paymentInformation != null)
            {
                // Set the invoice number
                SetCellValueAndStyle(worksheet, currentRow, 11, data.Invoice.InvoiceNr);

                // Set the payment type
                SetCellValueAndStyle(worksheet, currentRow, 12, paymentInformation.Type.ToString());

                // Set the payment amount
                SetCellValueAndStyle(worksheet, currentRow, 13, paymentInformation.Amount);

                // Set the payment date
                SetCellValueAndStyle(worksheet, currentRow, 14, FormatDateAsLocalTime(paymentInformation.PaidAt, data));

                // Set the payment reference
                SetCellValueAndStyle(worksheet, currentRow, 15, paymentInformation.Reference);
            }

            // Set the main business activity.
            SetCellValueAndStyle(worksheet, currentRow, 16, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CompanyMainActivity)?.Value);

            // Is using the accounting records tool.
            SetCellValueAndStyle(worksheet, currentRow, 17, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.UseAccountingTool)?.Value);

            // Is this the first financial report?
            SetCellValueAndStyle(worksheet, currentRow, 18, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.FirstFinancialReport)?.Value);

            // Set financial period start date
            SetCellValueAndStyle(worksheet, currentRow, 19, data.StartsAt.Value.ToString(WellKnownReportConstants.DateFormat));

            // Set financial period end date
            SetCellValueAndStyle(worksheet, currentRow, 20, data.EndsAt.Value.ToString(WellKnownReportConstants.DateFormat));

            // Set number of authorized shares
            SetCellValueAndStyle(worksheet, currentRow, 21, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.AuthorizeSharedNo)?.Value);

            // Set number of issued shares
            SetCellValueAndStyle(worksheet, currentRow, 22, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.IssuedSharedNo)?.Value);

            // Set the value per share
            SetCellValueAndStyle(worksheet, currentRow, 23, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.ValuePerShare)?.Value);

            // Determine the investment type
            var paidInCash = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.EquityPaidInCash)?.Value;

            var propertyTransfer = data.FormDocument.Attributes.FirstOrDefault(a =>
            a.Key == WellKnownFormDocumentAttibuteKeys.EquityPropertyTransfer)?.Value;

            var securitiesTransfer = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.EquitySecuritiesTransfer)?.Value;

            // Set the investment type
            SetCellValueAndStyle(worksheet, currentRow, 24, paidInCash == "true" ? "CASH" : propertyTransfer == "true" ? "Property transfer" : securitiesTransfer == "true" ? "Security transfer" : "");

            // Set if the investment type was cash
            SetCellValueAndStyle(worksheet, currentRow, 25, paidInCash == "true" ? "Yes" : "No");

            // Set the invested cash
            SetCellValueAndStyle(worksheet, currentRow, 26, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.EquityPaidCash)?.Value);

            // Set if the investment type was property transfer
            SetCellValueAndStyle(worksheet, currentRow, 27, propertyTransfer == "true" ? "Yes" : "No");

            // Set if the investment type was securities transfer
            SetCellValueAndStyle(worksheet, currentRow, 28, securitiesTransfer == "true" ? "Yes" : "No");

            // Set the transfered security investments
            SetCellValueAndStyle(worksheet, currentRow, 29, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.EquitySecuritiesValue)?.Value);

            // Set the total interest income received
            SetCellValueAndStyle(worksheet, currentRow, 30, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.TotalInterestIncome)?.Value);

            // Set the total dividend income received
            SetCellValueAndStyle(worksheet, currentRow, 31, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.TotalDividendIncome)?.Value);

            // Set the total other income received
            SetCellValueAndStyle(worksheet, currentRow, 32, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.TotalOtherIncome)?.Value);

            // Set the total income earned but not yet received
            SetCellValueAndStyle(worksheet, currentRow, 33, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.EndingInterestReceivable)?.Value);

            // Retrieve the total other income earned but not yet received
            var otherEndingInterestReceived = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherEndingInterestReceivable, StringComparison.InvariantCultureIgnoreCase)).ToList();

            // Calculate the total if any exist
            if (otherEndingInterestReceived.Count > 0)
            {
                var amounts = otherEndingInterestReceived.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int total = amounts.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 34, total);
            }

            // Set the total prior period income received
            SetCellValueAndStyle(worksheet, currentRow, 35, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.BeginningInterestReceivable)?.Value);

            // Retrieve the total other prior income received
            var otherPriorIncomeReceived = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherBeginningInterestreceivable, StringComparison.InvariantCultureIgnoreCase)).ToList();

            // Calculate the total if any exist
            if (otherPriorIncomeReceived.Count > 0)
            {
                var amounts = otherPriorIncomeReceived.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int total = amounts.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 36, total);
            }

            // Set the total proceeds
            SetCellValueAndStyle(worksheet, currentRow, 38, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.TotalProceeds)?.Value);

            // Set the total security investments market value
            SetCellValueAndStyle(worksheet, currentRow, 39, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.TotalMarketValue)?.Value);

            // Set the total security investments purchases cost
            SetCellValueAndStyle(worksheet, currentRow, 40, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.TotalPurchaseCost)?.Value);

            // Set the portfolio management fees
            SetCellValueAndStyle(worksheet, currentRow, 41, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.PortfolioManagementFeesPeriod)?.Value);

            // Set the company administration fees
            SetCellValueAndStyle(worksheet, currentRow, 42, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CompanyAdministrationFeesPeriod)?.Value);

            // Set the loan interest payments
            SetCellValueAndStyle(worksheet, currentRow, 43, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.LoanInterestPayments)?.Value);

            // Set the other expenses bank charges
            SetCellValueAndStyle(worksheet, currentRow, 44, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.BankCharges)?.Value);

            // Set the other expenses bank charges
            SetCellValueAndStyle(worksheet, currentRow, 45, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.TaxWithheld)?.Value);

            // Set the portfolio management fees
            SetCellValueAndStyle(worksheet, currentRow, 46, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.PortfolioManagementFees)?.Value);

            // Set the company administration fees
            SetCellValueAndStyle(worksheet, currentRow, 47, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CompanyAdministrationFees)?.Value);

            // Retrieve the total other expenses
            var otherExpenses = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherCompanyExpenses, StringComparison.InvariantCultureIgnoreCase)).ToList();

            // Calculate the other expenses
            if (otherExpenses.Count > 0)
            {
                var amounts = otherExpenses.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int total = amounts.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 48, total);
            }

            // Set the prior period expenses
            SetCellValueAndStyle(worksheet, currentRow, 49, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.BeginningInterestPayable)?.Value);

            // Retrieve the total other prior expenses
            var otherPriorExpenses = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherPeriodPaidExpenses, StringComparison.InvariantCultureIgnoreCase)).ToList();

            // Calculate the other prior expenses
            if (otherPriorExpenses.Count > 0)
            {
                var amounts = otherPriorExpenses.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int total = amounts.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 50, total);
            }

            // Set the expenses incurred but not yet paid
            SetCellValueAndStyle(worksheet, currentRow, 51, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.EndingInterestPayableLoans)?.Value);

            // Retrieve the total other prior expenses
            var otherExpensesIncurred = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherPeriodNotPaidExpenses, StringComparison.InvariantCultureIgnoreCase)).ToList();

            // Calculate the other expenses incurred
            if (otherExpensesIncurred.Count > 0)
            {
                var amounts = otherExpensesIncurred.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int total = amounts.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 52, total);
            }

            // Set the dividends paid to shareholders
            SetCellValueAndStyle(worksheet, currentRow, 53, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.DividendsPaidShareholders)?.Value);

            // Set the dividends declared but not yet paid
            SetCellValueAndStyle(worksheet, currentRow, 54, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.DividendsNotPaidShareholders)?.Value);

            // Set the total fund with drawn
            SetCellValueAndStyle(worksheet, currentRow, 55, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.TotalPaymentsPurchaseSecuritiesInvestments)?.Value);

            // Set if the company has any liabilities to pay
            SetCellValueAndStyle(worksheet, currentRow, 56, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CompanyLiabilities)?.Value);

            // Retrieve the liabilities loans
            var liabilityLoans = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Loans, StringComparison.InvariantCultureIgnoreCase)).ToList();

            // Calculate the liabilities
            if (liabilityLoans.Count > 1)
            {
                var currents = liabilityLoans.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Current, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int totalCurrent = currents.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 57, totalCurrent);

                var nonCurrents = liabilityLoans.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Current, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int totalNonCurrent = currents.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 58, totalNonCurrent);
            }

            // Retrieve the payable accounts
            var payableAccounts = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.AccountPayableAccrual, StringComparison.InvariantCultureIgnoreCase)).ToList();

            // Calculate the liabilities
            if (payableAccounts.Count > 0)
            {
                var currents = payableAccounts.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Current, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int totalCurrent = currents.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 59, totalCurrent);

                var nonCurrents = payableAccounts.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.NonCurrent, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int totalNonCurrent = currents.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 60, totalNonCurrent);
            }

            // Retrieve the other liabilities
            var otherliabilities = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherLiabilities, StringComparison.InvariantCultureIgnoreCase)).ToList();

            // Calculate the liabilities
            if (otherliabilities.Count > 0)
            {
                var currents = otherliabilities.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Current, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int totalCurrent = currents.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 61, totalCurrent);

                var nonCurrents = otherliabilities.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.NonCurrent, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int totalNonCurrent = currents.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 62, totalNonCurrent);
            }

            // Set if the company own any assets
            SetCellValueAndStyle(worksheet, currentRow, 63, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CompanyAssets)?.Value);

            // Retrieve the purchase cost for assets
            var assets = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.FixedAssets, StringComparison.InvariantCultureIgnoreCase)).ToList();

            // Calculate the purchase cost of assets
            if (assets.Count > 0)
            {
                var amounts = assets.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.PurchaseCost, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int total = amounts.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 64, total);
            }

            // Calculate the assesed cost of assets
            if (assets.Count > 0)
            {
                var amounts = assets.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.AssessedValue, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int total = amounts.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 65, total);
            }

            // Set if the assests were paid in cash from the bank
            SetCellValueAndStyle(worksheet, currentRow, 66, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.AssetsPaidInCash)?.Value);

            // Set if the cash paid from the bank
            SetCellValueAndStyle(worksheet, currentRow, 67, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.AmountPaid)?.Value);

            // Set if the company owns any cash or equivalent
            SetCellValueAndStyle(worksheet, currentRow, 68, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CompanyCash)?.Value);

            // Retrieve the banck details
            var bankDetails = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.CashBankAccounts, StringComparison.InvariantCultureIgnoreCase)).ToList();

            // Calculate the purchase cost of assets
            if (bankDetails.Count > 0)
            {
                var amounts = bankDetails.Where(a => a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase)).Select(a => a.Value).ToList();
                int total = amounts.Sum(x => Convert.ToInt32(x));

                // Set the value
                SetCellValueAndStyle(worksheet, currentRow, 69, total);
            }

            // Set the cash setup for capital
            var cashSetupCapital = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CashSetupCapital)?.Value;

            SetCellValueAndStyle(worksheet, currentRow, 70, cashSetupCapital);

            // Set the cash received from income
            var cashReceivedIncome = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CashReceivedIncome)?.Value;

            SetCellValueAndStyle(worksheet, currentRow, 71, cashReceivedIncome);

            // Set the cash paid for expenses
            var cashPaidExpenses = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CashPaidExpenses)?.Value;

            SetCellValueAndStyle(worksheet, currentRow, 72, cashPaidExpenses);

            // Set the cash received from loans
            var cashReceivedPaidLoans = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CashReceivedPaidLoans)?.Value;
            SetCellValueAndStyle(worksheet, currentRow, 73, cashReceivedPaidLoans);

            // Set the cash received from investments sold
            var cashReceivedInvestmentsSold = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CashReceivedInvestmentsSold)?.Value;

            SetCellValueAndStyle(worksheet, currentRow, 74, cashReceivedInvestmentsSold);

            // Set the cash received from investments purchased
            var cashPaidInvestmentsPurchased = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CashPaidInvestmentsPurchased)?.Value;

            SetCellValueAndStyle(worksheet, currentRow, 75, cashPaidInvestmentsPurchased);

            // Set the cash received from sales of non current assets
            var cashReceivedSalePaidNonCurrentAssets = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CashReceivedSalePaidNonCurrentAssets)?.Value;

            SetCellValueAndStyle(worksheet, currentRow, 76, cashReceivedSalePaidNonCurrentAssets);

            // Set the othet cash transactions
            var otherCashTransactions = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.CashReceivedSalePaidNonCurrentAssets)?.Value;

            SetCellValueAndStyle(worksheet, currentRow, 77, otherCashTransactions);

            // Calculate the total cash balance
            var totalCashBalance = cashSetupCapital.ToIntOrDefault() + cashReceivedIncome.ToIntOrDefault() + cashPaidExpenses.ToIntOrDefault() +
                cashReceivedPaidLoans.ToIntOrDefault() + cashReceivedInvestmentsSold.ToIntOrDefault() + cashPaidInvestmentsPurchased.ToIntOrDefault() +
                cashReceivedSalePaidNonCurrentAssets.ToIntOrDefault() + otherCashTransactions.ToIntOrDefault();

            // Set the total cash balance
            SetCellValueAndStyle(worksheet, currentRow, 78, totalCashBalance);

            // Set if the user want to review previous information
            SetCellValueAndStyle(worksheet, currentRow, 79, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.ReviewPreviousInfo)?.Value);

            // Set if financial reports are accurate
            SetCellValueAndStyle(worksheet, currentRow, 80, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.FinancialReportsAccurate)?.Value);

            // Set if the information is correct
            SetCellValueAndStyle(worksheet, currentRow, 81, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.ConfirmationDeclaration)?.Value);

            // Set if the information for assets is correct
            SetCellValueAndStyle(worksheet, currentRow, 82, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.ConfirmationAssets)?.Value);

            // Set if the information for funds is correct
            SetCellValueAndStyle(worksheet, currentRow, 83, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.ConfirmationFunds)?.Value);

            // Set if the information for reports is correct
            SetCellValueAndStyle(worksheet, currentRow, 83, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.ConfirmationReports)?.Value);

            // Set if the information for tax advide is correct
            SetCellValueAndStyle(worksheet, currentRow, 83, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.ConfirmationTaxAdvice)?.Value);

            // Set if the information for fee acknowledge is correct
            SetCellValueAndStyle(worksheet, currentRow, 83, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.ConfirmationFeeAcknowledgement)?.Value);

            // Set the person stating the declaration
            SetCellValueAndStyle(worksheet, currentRow, 83, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.DeclarantName)?.Value);

            // Set the relation to the entity
            SetCellValueAndStyle(worksheet, currentRow, 83, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.EntityRelation)?.Value);

            // Set the other relation to the entity
            SetCellValueAndStyle(worksheet, currentRow, 83, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.OtherEntityRelation)?.Value);

            // Set the declaration phone number
            var declarationPhoneNumber = data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.Prefix)?.Value + data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.PhoneNumber)?.Value;

            SetCellValueAndStyle(worksheet, currentRow, 83, declarationPhoneNumber);

            // Set the declaration email
            SetCellValueAndStyle(worksheet, currentRow, 83, data.FormDocument.Attributes.FirstOrDefault(a =>
                a.Key == WellKnownFormDocumentAttibuteKeys.DeclarantEmail)?.Value);
        }
    }
}