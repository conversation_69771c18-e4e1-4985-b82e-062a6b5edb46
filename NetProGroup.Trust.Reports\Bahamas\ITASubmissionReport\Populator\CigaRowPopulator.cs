// <copyright file="CigaRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class CigaRowPopulator : LinePopulatorBase, ICigaRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var form = data.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;
            var relevantActivityIndexes = GetRelevantActivityIndexes(form!.DataSet);
            
            List<int> GetRelevantActivityIndexes(Dictionary<string, string> dataSet)
            {
                var pattern = $@"{FormKeys.RelevantActivitiesRegexPrefix}\.(\d+)\.";
                return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
            }

            foreach (var index in relevantActivityIndexes)
            {
                // Check if the activity was selected
                var isSelected = bool.Parse(GetValueOrDefault(form, FormKeys.RelevantActivitiesIsSelected(index), "false"));

                if (isSelected)
                {
                    // Retrieve the name
                    var relevantActivity = GetValueOrDefault(form, FormKeys.RelevantActivitiesLabel(index));

                    // Check the selected activity
                    string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                    // Get CIGA activity indexes for this relevant activity
                    var cigaActivityIndexes = GetCigaActivityIndexes(form!.DataSet, relevantActivityKey);

                    List<int> GetCigaActivityIndexes(Dictionary<string, string> dataSet, string activityKey)
                    {
                        var pattern = $@"{activityKey.Replace(".", "\\.", StringComparison.Ordinal)}{FormKeys.ActivitiesRegexSuffix}\.(\d+)\.";
                        return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
                    }

                    foreach (var cigaIndex in cigaActivityIndexes)
                    {
                        // Retrieve the entity unique id
                        SetCellValueAndStyle(worksheet, currentRow, 1, GetValueOrDefault(form, FormKeys.EntityDetailsEntityId));

                        // Retrieve the name
                        var activityName = GetValueOrDefault(form, FormKeys.CigaActivitiesLabel(relevantActivityKey, cigaIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 2, activityName);

                        SetCellValueAndStyle(worksheet, currentRow, 3, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsEndDate));

                        // Retrieve the ciga code
                        var cigaCode = GetValueOrDefault(form, FormKeys.CigaActivitiesDescription(relevantActivityKey, cigaIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 4, cigaCode);

                        currentRow += 1;
                    }
                }
            }
        }
    }
}