// <copyright file="PremisesRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class PremisesRowPopulator : LinePopulatorBase, IPremisesRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var form = data.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;
            var relevantActivityIndexes = GetRelevantActivityIndexes(form!.DataSet);

            List<int> GetRelevantActivityIndexes(Dictionary<string, string> dataSet)
            {
                var pattern = $@"{FormKeys.RelevantActivitiesRegexPrefix}\.(\d+)\.";
                return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
            }

            foreach (var index in relevantActivityIndexes)
            {
                // Check if the activity was selected
                var isSelected = bool.Parse(GetValueOrDefault(form, FormKeys.RelevantActivitiesIsSelected(index), "false"));

                if (isSelected)
                {
                    // Retrieve the name
                    var relevantActivity = GetValueOrDefault(form, FormKeys.RelevantActivitiesLabel(index));

                    // Check the selected activity
                    string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                    // Get premise indexes for this relevant activity
                    var premiseIndexes = GetPremiseIndexes(form!.DataSet, relevantActivityKey);

                    List<int> GetPremiseIndexes(Dictionary<string, string> dataSet, string activityKey)
                    {
                        var pattern = $@"{activityKey.Replace(".", "\\.", StringComparison.Ordinal)}{FormKeys.PremisesRegexSuffix}\.(\d+)\.";
                        return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
                    }

                    foreach (var premiseIndex in premiseIndexes)
                    {
                        // Retrieve the entity unique id
                        SetCellValueAndStyle(worksheet, currentRow, 1, GetValueOrDefault(form, FormKeys.EntityDetailsEntityId));

                        // Retrieve the name
                        SetCellValueAndStyle(worksheet, currentRow, 2, relevantActivity);

                        SetCellValueAndStyle(worksheet, currentRow, 3, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsEndDate));

                        // Retrieve the premise address line 1
                        var addressLine1 = GetValueOrDefault(form, FormKeys.PremisesAddressLine1(relevantActivityKey, premiseIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 4, addressLine1);

                        // Retrieve the premise address line 2
                        var addressLine2 = GetValueOrDefault(form, FormKeys.PremisesAddressLine2(relevantActivityKey, premiseIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 5, addressLine2);

                        // Retrieve the premise country
                        var country = GetValueOrDefault(form, FormKeys.PremisesPremiseCountry(relevantActivityKey, premiseIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 6, country);

                        currentRow += 1;
                    }
                }
            }
        }
    }
}